package aws

import (
	"context"
	"fmt"
	"mime/multipart"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	awsCfg "github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/aws"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/config"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/utils"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

type AwsDomainItf interface {
	UploadFile(ctx context.Context, file *multipart.FileHeader, fileName string) (string, error)
}

type AwsResourceItf interface{}

func (c *AwsDomain) UploadFile(ctx context.Context, file *multipart.FileHeader, fileName string) (string, error) {
	s3Client := awsCfg.S3Manager()
	cfg := config.GetConfig()

	reader, err := utils.ReadMultipartFile(file)
	if err != nil {
		return "", log.LogError(err, nil)
	}

	contentType := file.Header.Get("Content-Type")

	_, err = s3Client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:      aws.String(cfg.AwsS3Bucket),
		Key:         aws.String(fileName),
		Body:        reader,
		ContentType: aws.String(contentType),
	})
	if err != nil {
		return "", log.LogError(err, nil)
	}

	fileURL := generateS3FileURL(cfg.AwsBaseUrl, fileName)
	return fileURL, nil
}

func generateS3FileURL(baseUrl, fileName string) string {
	return fmt.Sprintf("%s/%s", baseUrl, fileName)
}
